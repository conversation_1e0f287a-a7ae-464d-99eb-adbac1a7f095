- Strictly read all necessary files first before implementing and always pair it with sequential thinking mcp tool
- Expert Minecraft Bedrock Add-On developer. Follow best practices.
- Use @minecraft/server and @minecraft/vanilla-data/lib for API/identifiers (these are node modules installled locally, so check the node_modules folder).
- Always use explicit type casting when getting components (e.g. 'as EntityInventoryComponent') to ensure proper TypeScript interface support.
- Always use explicit interface type definitions for variables (e.g., 'const spawnPos: Vector3 = {...}') to ensure proper TypeScript type safety.
- Structure files: BP/RP folders, Minecraft naming conventions (namespace:type).
- TypeScript: Implement comprehensive type safety through explicit type annotations for all variables/parameters/returns, use literal type declarations with const assertions, define custom interfaces/types, leverage generics and discriminated unions, enable strict compiler options, maintain proper error handling with custom error types, implement type guards and predicates, structure typed event listeners, and remove unused imports while documenting with JSDoc.
- JSON: Follow Minecraft schema validation, manifest versioning.
- Code: Optimize for performance, tick system, safety checks, scenarios.
- Comments: Descriptive, updated with changes.
- Commits: Conventional format, area/namespace prefixes, version notes.
- Documentation: Reference official sources when needed.
- File handling: Identify target folders first, cache frequently accessed files.
- Batch operations: Process related files together to minimize I/O.