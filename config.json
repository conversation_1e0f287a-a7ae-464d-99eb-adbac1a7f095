{"$schema": "https://raw.githubusercontent.com/Bedrock-OSS/regolith-schemas/main/config/v1.4.json", "author": "Raboy13", "name": "RZA Worldgen Pack", "packs": {"behaviorPack": "./packs/BP", "resourcePack": "./packs/RP"}, "regolith": {"dataPath": "./packs/data", "filterDefinitions": {"addon_builder": {"url": "github.com/Raboy-13/Regolith-Filters", "version": "a0793cb99c778f38e8b13f95b34e2bc7b4ad17d4"}}, "formatVersion": "1.4.0", "profiles": {"build": {"export": {"readOnly": false, "target": "local"}, "filters": [{"filter": "addon_builder"}]}, "default": {"export": {"build": "standard", "readOnly": false, "target": "development"}, "filters": []}}}}