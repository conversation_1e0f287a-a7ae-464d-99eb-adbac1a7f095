{"format_version": "1.13.0", "minecraft:feature_rules": {"description": {"identifier": "rza:gold_ore.feature_rule", "places_feature": "minecraft:gold_ore_feature"}, "conditions": {"placement_pass": "underground_pass", "minecraft:biome_filter": [{"test": "has_biome_tag", "value": "wasteland"}]}, "distribution": {"iterations": 2, "coordinate_eval_order": "zyx", "x": {"distribution": "uniform", "extent": [0, 16]}, "y": {"distribution": "uniform", "extent": [-64, 32]}, "z": {"distribution": "uniform", "extent": [0, 16]}}}}