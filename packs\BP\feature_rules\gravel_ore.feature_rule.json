{"format_version": "1.13.0", "minecraft:feature_rules": {"description": {"identifier": "rza:gravel_ore.feature_rule", "places_feature": "minecraft:gravel_ore_feature"}, "conditions": {"placement_pass": "underground_pass", "minecraft:biome_filter": [{"test": "has_biome_tag", "value": "wasteland"}]}, "distribution": {"iterations": 8, "coordinate_eval_order": "zyx", "x": {"distribution": "uniform", "extent": [0, 16]}, "y": {"distribution": "uniform", "extent": [-64, 128]}, "z": {"distribution": "uniform", "extent": [0, 16]}}}}