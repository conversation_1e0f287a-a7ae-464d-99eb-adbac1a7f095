{"format_version": "1.13.0", "minecraft:feature_rules": {"description": {"identifier": "rza:ground_cover.feature_rule", "places_feature": "rza:scatter_ground_cover.feature"}, "conditions": {"placement_pass": "before_surface_pass", "minecraft:biome_filter": [{"test": "has_biome_tag", "value": "wasteland"}]}, "distribution": {"iterations": 32, "x": {"distribution": "gaussian", "extent": [0, 24]}, "y": "query.heightmap(variable.worldx, variable.worldz)", "z": {"distribution": "gaussian", "extent": [0, 24]}}}}