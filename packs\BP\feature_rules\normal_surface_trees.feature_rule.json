{"format_version": "1.13.0", "minecraft:feature_rules": {"description": {"identifier": "rza:normal_surface_trees.feature_rule", "places_feature": "rza:dead_oak_tree.feature"}, "conditions": {"placement_pass": "surface_pass", "minecraft:biome_filter": [{"all_of": [{"test": "has_biome_tag", "operator": "!=", "value": "swamp"}, {"test": "has_biome_tag", "operator": "==", "value": "wasteland"}]}]}, "distribution": {"iterations": "(query.noise((variable.originx + 32) / 200.0, (variable.originz + 32) / 200.0) < -0.8) ? 4 : 8", "x": {"distribution": "uniform", "extent": [0, 16]}, "y": {"distribution": "uniform", "extent": [0, "query.heightmap(variable.worldx, variable.worldz)"]}, "z": {"distribution": "uniform", "extent": [0, 16]}}}}