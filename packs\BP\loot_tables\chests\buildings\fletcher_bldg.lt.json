{"pools": [{"rolls": {"min": 3, "max": 7}, "entries": [{"type": "empty", "weight": 30}, {"type": "item", "name": "minecraft:bow", "weight": 15, "functions": [{"function": "set_damage", "damage": {"min": 0.1, "max": 0.5}}, {"function": "enchant_randomly", "chance": 0.3}]}, {"type": "item", "name": "minecraft:feather", "weight": 15, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}]}, {"type": "item", "name": "minecraft:stick", "weight": 15, "functions": [{"function": "set_count", "count": {"min": 4, "max": 16}}]}, {"type": "item", "name": "minecraft:flint", "weight": 12, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}]}]}, {"rolls": {"min": 1, "max": 3}, "entries": [{"type": "empty", "weight": 30}, {"type": "item", "name": "minecraft:crossbow", "weight": 8, "functions": [{"function": "set_damage", "damage": {"min": 0.1, "max": 0.5}}, {"function": "enchant_randomly", "chance": 0.2}]}, {"type": "item", "name": "minecraft:string", "weight": 15, "functions": [{"function": "set_count", "count": {"min": 2, "max": 6}}]}, {"type": "item", "name": "minecraft:tripwire_hook", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}]}, {"rolls": {"min": 1, "max": 2}, "entries": [{"type": "empty", "weight": 35}, {"type": "item", "name": "minecraft:emerald", "weight": 10, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:fletching_table", "weight": 3}]}, {"rolls": {"min": 2, "max": 4}, "entries": [{"type": "empty", "weight": 25}, {"type": "item", "name": "minecraft:arrow", "weight": 20, "functions": [{"function": "set_count", "count": {"min": 4, "max": 16}}]}, {"type": "item", "name": "minecraft:arrow", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 6}]}, {"type": "item", "name": "minecraft:arrow", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 8}]}, {"type": "item", "name": "minecraft:arrow", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 13}]}, {"type": "item", "name": "minecraft:arrow", "weight": 4, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 18}]}, {"type": "item", "name": "minecraft:arrow", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 22}]}, {"type": "item", "name": "minecraft:arrow", "weight": 4, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 26}]}, {"type": "item", "name": "minecraft:arrow", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 32}]}, {"type": "item", "name": "minecraft:arrow", "weight": 4, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 35}]}, {"type": "item", "name": "minecraft:arrow", "weight": 2, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 37}]}, {"type": "item", "name": "minecraft:arrow", "weight": 3, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}, {"function": "set_data", "data": 41}]}]}]}