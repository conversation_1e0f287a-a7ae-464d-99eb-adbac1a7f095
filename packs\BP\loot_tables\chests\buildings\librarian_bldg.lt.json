{"pools": [{"rolls": {"min": 3, "max": 7}, "entries": [{"type": "empty", "weight": 30}, {"type": "item", "name": "minecraft:book", "weight": 20, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:paper", "weight": 15, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}]}, {"type": "item", "name": "minecraft:feather", "weight": 10, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:ink_sac", "weight": 10, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}]}, {"rolls": {"min": 1, "max": 3}, "entries": [{"type": "empty", "weight": 25}, {"type": "item", "name": "minecraft:book", "weight": 10, "functions": [{"function": "enchant_randomly", "chance": 0.4}]}, {"type": "item", "name": "minecraft:bookshelf", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "minecraft:lectern", "weight": 3}]}, {"rolls": {"min": 1, "max": 2}, "entries": [{"type": "item", "name": "minecraft:emerald", "weight": 10, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:name_tag", "weight": 5}, {"type": "item", "name": "minecraft:writable_book", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "empty", "weight": 35}]}]}