import { BlockPermutation } from '@minecraft/server';
export class GrassComponent {
    beforeOnPlayerPlace(ev) {
        const block = ev.block;
        const above = block.above(1);
        const blockTypeId = ev.permutationToPlace.type.id;
        if (!above?.isAir) {
            ev.cancel = true;
            return;
        }
        if (blockTypeId === 'rza:tall_grass')
            above.setPermutation(BlockPermutation.resolve('rza:tall_grass', { 'rza:top_bit': true }));
        else if (blockTypeId === 'rza:large_fern')
            above.setPermutation(BlockPermutation.resolve('rza:large_fern', { 'rza:top_bit': true }));
        return;
    }
    onPlayerDestroy(ev) {
        const block = ev.block;
        const below = block.below(1);
        if (below?.permutation.getState('rza:top_bit') === false) {
            const loc = below.center();
            below.dimension.runCommand(`setblock ${loc.x} ${loc.y} ${loc.z} air [] destroy`);
        }
        return;
    }
}
